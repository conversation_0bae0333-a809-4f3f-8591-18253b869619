#!/usr/bin/env python3
"""
Main Application Controller
Coordinates between different modules and handles application flow
"""

import streamlit as st
import os
import time
from datetime import datetime
from typing import Optional

# Import custom modules
import sys
sys.path.append('.')
from src.services.spam_detector import SpamDetector
from src.app.streamlit_facebook import <PERSON><PERSON><PERSON>
from src.app.streamlit_monitor import AutoMonitor
from config.app_config import config

# Import UI and page modules
from src.app.ui_components import NotificationManager, load_custom_css
from src.app.dashboard import DashboardRenderer
from src.app.page_modules.manual_check import ManualCheckPage
from src.app.page_modules.pending_spam import PendingSpamPage
from src.app.page_modules.test_detector import TestDetectorPage
from src.app.page_modules.settings import SettingsPage
from src.app.page_modules.logs import LogsPage


class StreamlitJudolRemover:
    """Main application controller that coordinates between different modules"""
    
    def __init__(self):
        self.initialize_session_state()
        self.load_environment()
        self.initialize_services()
        # Page renderers will be initialized when needed in run() method
    
    def initialize_session_state(self):
        """Initialize Streamlit session state variables"""
        if 'spam_detector' not in st.session_state:
            st.session_state.spam_detector = None
        if 'facebook_api' not in st.session_state:
            st.session_state.facebook_api = None
        if 'auto_monitor' not in st.session_state:
            st.session_state.auto_monitor = None
        if 'monitor_running' not in st.session_state:
            st.session_state.monitor_running = False
        if 'posts_cache' not in st.session_state:
            st.session_state.posts_cache = {}
        if 'comments_cache' not in st.session_state:
            st.session_state.comments_cache = {}
        if 'statistics' not in st.session_state:
            st.session_state.statistics = {
                'comments_processed': 0,
                'spam_removed': 0,
                'spam_detected': 0,
                'last_check': None,
                'start_time': None
            }
        # Initialize logs-related session state
        if 'monitor_logs' not in st.session_state:
            st.session_state.monitor_logs = []
        if 'auto_refresh_enabled' not in st.session_state:
            st.session_state.auto_refresh_enabled = True
        if 'current_page' not in st.session_state:
            st.session_state.current_page = "Dashboard"
        if 'previous_page' not in st.session_state:
            st.session_state.previous_page = "Dashboard"
        if 'auto_delete_enabled' not in st.session_state:
            st.session_state.auto_delete_enabled = os.getenv('AUTO_DELETE_SPAM', 'true').lower() == 'true'
        if 'notifications' not in st.session_state:
            st.session_state.notifications = []
        if 'monitor_logs' not in st.session_state:
            st.session_state.monitor_logs = []
        if 'pending_spam' not in st.session_state:
            st.session_state.pending_spam = []
        if 'current_page' not in st.session_state:
            st.session_state.current_page = "Dashboard"
        if 'previous_page' not in st.session_state:
            st.session_state.previous_page = None
    
    def load_environment(self):
        """Load environment variables"""
        try:
            from dotenv import load_dotenv
            # Load .env from project root (two levels up from src/app/)
            env_path = os.path.join(os.path.dirname(__file__), '..', '..', '.env')
            load_dotenv(env_path)
        except ImportError:
            st.warning("python-dotenv not installed. Make sure environment variables are set.")

        self.page_id = os.getenv('PAGE_ID')
        self.page_access_token = os.getenv('PAGE_ACCESS_TOKEN')
        self.model_path = os.getenv('MODEL_PATH', './src/models')
        self.confidence_threshold = float(os.getenv('CONFIDENCE_THRESHOLD', '0.5'))
    
    def initialize_services(self):
        """Initialize spam detector and Facebook API"""
        try:
            # Initialize spam detector
            if st.session_state.spam_detector is None:
                with st.spinner("Loading spam detection model..."):
                    st.session_state.spam_detector = SpamDetector(self.model_path)

            # Initialize Facebook API
            if st.session_state.facebook_api is None and self.page_access_token:
                st.session_state.facebook_api = FacebookAPI(
                    self.page_id,
                    self.page_access_token
                )

        except Exception as e:
            NotificationManager.show_notification(f"Error initializing services: {str(e)}", "error", 8000)
    
    def initialize_page_renderers(self):
        """Initialize page renderer instances"""
        try:
            # Check if renderers already exist to avoid re-initialization
            if not hasattr(self, 'dashboard_renderer'):
                self.dashboard_renderer = DashboardRenderer(
                    st.session_state.facebook_api,
                    st.session_state.spam_detector,
                    self.confidence_threshold
                )

            if not hasattr(self, 'manual_check_page'):
                self.manual_check_page = ManualCheckPage(
                    st.session_state.facebook_api,
                    st.session_state.spam_detector,
                    self.confidence_threshold
                )

            if not hasattr(self, 'pending_spam_page'):
                self.pending_spam_page = PendingSpamPage(st.session_state.facebook_api)

            if not hasattr(self, 'test_detector_page'):
                self.test_detector_page = TestDetectorPage(
                    st.session_state.spam_detector,
                    self.confidence_threshold
                )

            if not hasattr(self, 'settings_page'):
                self.settings_page = SettingsPage(
                    self.page_id,
                    self.page_access_token,
                    self.model_path,
                    self.confidence_threshold
                )

            if not hasattr(self, 'logs_page'):
                self.logs_page = LogsPage()



        except Exception as e:
            st.error(f"❌ Error initializing page renderers: {str(e)}")
            st.exception(e)
    
    def render_sidebar(self):
        """Render sidebar with navigation and controls"""
        st.sidebar.markdown("## 🛡️ Judol Remover")
        st.sidebar.markdown("---")

        # Navigation with URL routing
        page_options = ["Dashboard", "Manual Check", "Pending Spam", "Test Detector", "Settings", "Logs"]
        page_urls = ["dashboard", "manual-check", "pending-spam", "test-detector", "settings", "logs"]

        # Get current page from URL params or session state
        query_params = st.query_params
        current_page_url = query_params.get("page", "dashboard")

        # Map URL to page name
        url_to_page = dict(zip(page_urls, page_options))
        page_to_url = dict(zip(page_options, page_urls))

        current_page = url_to_page.get(current_page_url, "Dashboard")

        try:
            current_index = page_options.index(current_page)
        except ValueError:
            current_index = 0  # Default to Dashboard if current_page is not in list

        selected_page = st.sidebar.selectbox(
            "Navigate to:",
            page_options,
            index=current_index,
            key="page_selector"
        )

        # Update URL when page changes
        if selected_page != current_page:
            new_url = page_to_url[selected_page]
            st.query_params["page"] = new_url
            st.rerun()

        page = selected_page
        
        st.sidebar.markdown("---")
        
        # Monitor controls
        st.sidebar.markdown("### 🔄 Auto Monitor")

        # Auto Delete Toggle
        auto_delete = st.sidebar.checkbox(
            "🗑️ Auto Delete Spam",
            value=st.session_state.auto_delete_enabled,
            help="Otomatis hapus komentar yang terdeteksi spam"
        )

        if auto_delete != st.session_state.auto_delete_enabled:
            st.session_state.auto_delete_enabled = auto_delete

            # Update auto monitor configuration if it exists
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                st.session_state.auto_monitor.update_config(auto_delete_enabled=auto_delete)

            NotificationManager.show_notification(f"Auto delete {'enabled' if auto_delete else 'disabled'}", "success", 2000)

        # Auto Refresh Toggle
        auto_refresh = st.sidebar.checkbox(
            "🔄 Auto Refresh UI",
            value=st.session_state.get('auto_refresh_enabled', False),
            help="Otomatis refresh dashboard setiap 5 detik untuk update real-time"
        )
        st.session_state.auto_refresh_enabled = auto_refresh

        if st.session_state.monitor_running:
            if st.sidebar.button("⏹️ Stop Monitor", type="secondary"):
                self.stop_monitor()

            # Force sync statistics from auto monitor for real-time updates
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                try:
                    monitor_stats = st.session_state.auto_monitor.get_statistics()
                    if monitor_stats:
                        st.session_state.statistics.update(monitor_stats)
                except Exception:
                    pass

            # Show monitor status with more frequent runtime updates
            if st.session_state.statistics['start_time']:
                # Calculate runtime with precise seconds
                runtime = datetime.now() - st.session_state.statistics['start_time']
                total_seconds = int(runtime.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                seconds = total_seconds % 60

                if hours > 0:
                    runtime_str = f"{hours}h {minutes}m {seconds}s"
                else:
                    runtime_str = f"{minutes}m {seconds}s"

                # Add live indicator
                st.sidebar.metric("Runtime", f"🔴 {runtime_str}", help="Updates every second")

            # Real-time metrics with smooth transitions
            st.sidebar.metric("Comments Processed", st.session_state.statistics['comments_processed'])
            st.sidebar.metric("Spam Detected", st.session_state.statistics['spam_detected'])
            st.sidebar.metric("Spam Removed", st.session_state.statistics['spam_removed'])

            # Show last update time for transparency
            if st.session_state.statistics.get('last_check'):
                last_update = st.session_state.statistics['last_check']
                if isinstance(last_update, datetime):
                    time_diff = (datetime.now() - last_update).total_seconds()
                    if time_diff < 60:
                        st.sidebar.caption(f"🔄 Updated {int(time_diff)}s ago")
                    else:
                        st.sidebar.caption(f"🔄 Updated {int(time_diff//60)}m ago")
        else:
            if st.sidebar.button("▶️ Start Monitor", type="primary"):
                self.start_monitor()
        
        st.sidebar.markdown("---")
        
        # System status
        st.sidebar.markdown("### 📊 System Status")

        # Model status
        model_status = "🟢 Ready" if st.session_state.spam_detector else "🔴 Not Loaded"
        st.sidebar.markdown(f"**Model:** {model_status}")

        # Facebook API status
        fb_status = "🟢 Connected" if st.session_state.facebook_api else "🔴 Not Connected"
        st.sidebar.markdown(f"**Facebook:** {fb_status}")

        # Auto Monitor status
        if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
            monitor_status = "🟢 Initialized"
        else:
            monitor_status = "🔴 Not Initialized"
        st.sidebar.markdown(f"**Auto Monitor:** {monitor_status}")

        return page

    def start_monitor(self):
        """Start the auto monitor"""
        try:
            if not st.session_state.facebook_api:
                NotificationManager.show_notification("Facebook API not connected", "error", 5000)
                return

            if not st.session_state.spam_detector:
                NotificationManager.show_notification("Spam detector not loaded", "error", 5000)
                return

            # Initialize auto monitor
            st.session_state.auto_monitor = AutoMonitor(
                st.session_state.facebook_api,
                st.session_state.spam_detector,
                poll_interval=30
            )

            # Configure auto monitor settings
            st.session_state.auto_monitor.confidence_threshold = self.confidence_threshold
            st.session_state.auto_monitor.auto_delete_enabled = st.session_state.auto_delete_enabled

            # Start monitoring
            st.session_state.auto_monitor.start()
            st.session_state.monitor_running = True
            st.session_state.statistics['start_time'] = datetime.now()

            NotificationManager.show_notification("Auto monitor started!", "success", 3000)

            # Force UI update to show Stop Monitor button immediately
            st.rerun()

        except Exception as e:
            NotificationManager.show_notification(f"Failed to start monitor: {str(e)}", "error", 6000)

    def stop_monitor(self):
        """Stop the auto monitor"""
        try:
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                st.session_state.auto_monitor.stop()

            st.session_state.monitor_running = False
            st.session_state.statistics['start_time'] = None

            NotificationManager.show_notification("Auto monitor stopped!", "info", 3000)

            # Force UI update to show Start Monitor button immediately
            st.rerun()

        except Exception as e:
            NotificationManager.show_notification(f"Error stopping monitor: {str(e)}", "error", 5000)

    def handle_auto_refresh(self):
        """Handle auto refresh functionality"""
        if (st.session_state.monitor_running and
            st.session_state.get('auto_refresh_enabled', False)):

            # Auto refresh every 5 seconds
            time.sleep(5)
            st.rerun()

    def clear_page_components(self):
        """Clear page-specific components to prevent bleeding between pages"""
        # Use the enhanced clearing method from NotificationManager
        NotificationManager.clear_page_specific_containers()

        # Clear any temporary containers or placeholders
        for key in list(st.session_state.keys()):
            if key.startswith('temp_') or key.startswith('placeholder_'):
                try:
                    del st.session_state[key]
                except:
                    pass

    def run(self):
        """Main application entry point"""
        # Set page config first (only once)
        try:
            st.set_page_config(
                page_title="Judol Remover",
                page_icon="🛡️",
                layout="wide",
                initial_sidebar_state="expanded"
            )
        except st.errors.StreamlitAPIException:
            # Page config already set, ignore
            pass

        # Load custom CSS
        load_custom_css()

        # Render sidebar and get current page
        current_page = self.render_sidebar()

        # Update current page in session state and clear components if page changed
        if st.session_state.current_page != current_page:
            st.session_state.previous_page = st.session_state.current_page
            st.session_state.current_page = current_page
            # CRITICAL: Force clear ALL components to prevent bleeding
            self.force_clear_all_page_components()
            self.clear_page_components()

        # Display notifications
        NotificationManager.display_notifications()

        # Route to appropriate page with proper isolation
        try:
            # Clear main content area before rendering new page
            main_container = st.container()

            with main_container:
                if current_page == "Dashboard":
                    # Initialize dashboard renderer if needed
                    if not hasattr(self, 'dashboard_renderer'):
                        self.dashboard_renderer = DashboardRenderer(
                            st.session_state.facebook_api,
                            st.session_state.spam_detector,
                            self.confidence_threshold
                        )
                    self.dashboard_renderer.render_dashboard()

                elif current_page == "Manual Check":
                    # Initialize manual check page if needed
                    if not hasattr(self, 'manual_check_page'):
                        self.manual_check_page = ManualCheckPage(
                            st.session_state.facebook_api,
                            st.session_state.spam_detector,
                            self.confidence_threshold
                        )
                    self.manual_check_page.render()

                elif current_page == "Pending Spam":
                    # Initialize pending spam page if needed
                    if not hasattr(self, 'pending_spam_page'):
                        self.pending_spam_page = PendingSpamPage(st.session_state.facebook_api)
                    self.pending_spam_page.render()

                elif current_page == "Test Detector":
                    # Initialize test detector page if needed
                    if not hasattr(self, 'test_detector_page'):
                        self.test_detector_page = TestDetectorPage(
                            st.session_state.spam_detector,
                            self.confidence_threshold
                        )
                    self.test_detector_page.render()

                elif current_page == "Settings":
                    # Initialize settings page if needed
                    if not hasattr(self, 'settings_page'):
                        self.settings_page = SettingsPage(
                            self.page_id,
                            self.page_access_token,
                            self.model_path,
                            self.confidence_threshold
                        )
                    self.settings_page.render()

                elif current_page == "Logs":
                    # Initialize logs page if needed
                    if not hasattr(self, 'logs_page'):
                        self.logs_page = LogsPage()
                    self.logs_page.render()

                else:
                    st.error(f"❌ Unknown page: {current_page}")

        except Exception as e:
            st.error(f"❌ Error rendering {current_page} page: {str(e)}")
            st.exception(e)

        # Handle auto refresh - ONLY for specific pages and conditions
        self.handle_auto_refresh_safely(current_page)

    def handle_auto_refresh_safely(self, current_page: str):
        """Handle auto refresh safely without interfering with page navigation"""
        # Only enable auto refresh for Dashboard and Logs pages
        if current_page not in ["Dashboard", "Logs"]:
            return

        # Only if monitor is running and auto refresh is enabled
        if not (st.session_state.monitor_running and
                st.session_state.get('auto_refresh_enabled', False)):
            return

        # CRITICAL: Force clear any residual components before auto refresh
        self.force_clear_all_page_components()

        # Use safe Streamlit refresh with proper component isolation
        self.implement_safe_streamlit_refresh(current_page)

    def force_clear_all_page_components(self):
        """Force clear all page components before auto refresh to prevent bleeding"""
        # Clear all page-specific containers
        NotificationManager.clear_page_specific_containers()

        # Clear any widget keys that might persist
        keys_to_clear = []
        for key in st.session_state.keys():
            if any(prefix in key for prefix in ['temp_', 'placeholder_', 'widget_', 'form_', 'expander_']):
                keys_to_clear.append(key)

        for key in keys_to_clear:
            try:
                del st.session_state[key]
            except:
                pass

        # Clear any cached page renderers to force fresh rendering
        page_renderers = ['dashboard_renderer', 'logs_page', 'settings_page',
                         'manual_check_page', 'pending_spam_page', 'test_detector_page']
        for renderer in page_renderers:
            if hasattr(self, renderer):
                try:
                    delattr(self, renderer)
                except:
                    pass

    def implement_js_auto_refresh(self, current_page: str):
        """Implement JavaScript-based auto refresh to avoid st.rerun() conflicts"""
        # Use meta refresh or JavaScript instead of st.rerun() for auto refresh
        auto_refresh_html = f"""
        <div id="auto-refresh-indicator" style="
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,150,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        ">
            🔄 Auto refresh: <span id="countdown">5</span>s
        </div>
        <script>
            let countdown = 5;
            const countdownElement = document.getElementById('countdown');
            const refreshInterval = setInterval(() => {{
                countdown--;
                if (countdownElement) {{
                    countdownElement.textContent = countdown;
                }}
                if (countdown <= 0) {{
                    clearInterval(refreshInterval);
                    // Force page refresh while preserving current page
                    const currentUrl = new URL(window.location);
                    currentUrl.searchParams.set('page', '{current_page.lower().replace(" ", "-")}');
                    currentUrl.searchParams.set('_refresh', Date.now());
                    window.location.href = currentUrl.toString();
                }}
            }}, 1000);
        </script>
        """

        # Display the auto refresh indicator
        st.markdown(auto_refresh_html, unsafe_allow_html=True)

    def implement_safe_streamlit_refresh(self, current_page: str):
        """Alternative: Safe Streamlit refresh with component isolation"""
        # Create isolated refresh container
        refresh_container_key = f"refresh_container_{current_page}_{int(time.time())}"

        if refresh_container_key not in st.session_state:
            st.session_state[refresh_container_key] = st.empty()

        with st.session_state[refresh_container_key].container():
            # Show countdown
            col1, col2 = st.columns([3, 1])
            with col1:
                st.caption("🔄 Auto refresh enabled")
            with col2:
                # Use a progress bar for countdown
                progress_bar = st.progress(0)
                status_text = st.empty()

                for i in range(5, 0, -1):
                    progress_bar.progress((5-i+1)/5)
                    status_text.text(f"{i}s")
                    time.sleep(1)

                # Clear the refresh container before rerun
                try:
                    del st.session_state[refresh_container_key]
                except:
                    pass

                # Force clear components before rerun
                self.force_clear_all_page_components()

                # Safe rerun with page context preservation
                st.query_params["page"] = current_page.lower().replace(" ", "-")
                st.rerun()


def main():
    """Application entry point"""
    app = StreamlitJudolRemover()
    app.run()


if __name__ == "__main__":
    main()
