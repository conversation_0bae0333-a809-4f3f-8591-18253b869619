#!/usr/bin/env python3
"""
Test Auto Refresh Fix
Test script to verify that auto refresh doesn't cause component bleeding
"""

import streamlit as st
import time
from datetime import datetime

def test_auto_refresh_isolation():
    """Test that auto refresh doesn't cause component bleeding between pages"""
    
    st.title("🔄 Auto Refresh Isolation Test")
    st.markdown("This test verifies that auto refresh doesn't cause components to bleed between pages.")
    
    # Navigation
    page = st.selectbox("Select Page:", ["Dashboard Test", "Logs Test", "Settings Test"])
    
    # Auto refresh toggle
    auto_refresh_enabled = st.checkbox("🔄 Enable Auto Refresh (5s)", value=False)
    
    st.markdown("---")
    
    # Track page changes and component state
    if 'test_previous_page' not in st.session_state:
        st.session_state.test_previous_page = None
    if 'test_components_rendered' not in st.session_state:
        st.session_state.test_components_rendered = {}
    if 'test_refresh_count' not in st.session_state:
        st.session_state.test_refresh_count = 0
    
    # Clear components when page changes
    if st.session_state.test_previous_page != page:
        st.success(f"✅ Page changed from {st.session_state.test_previous_page} to {page}")
        
        # Simulate component clearing
        clear_test_components()
        
        st.session_state.test_previous_page = page
        st.info("🧹 Components cleared for new page")
    
    # Render page content with isolation
    with st.container():
        if page == "Dashboard Test":
            render_dashboard_with_refresh_test(auto_refresh_enabled)
        elif page == "Logs Test":
            render_logs_with_refresh_test(auto_refresh_enabled)
        elif page == "Settings Test":
            render_settings_with_refresh_test(auto_refresh_enabled)
    
    # Show component tracking
    show_component_tracking()
    
    # Handle auto refresh if enabled
    if auto_refresh_enabled and page in ["Dashboard Test", "Logs Test"]:
        handle_safe_auto_refresh(page)

def clear_test_components():
    """Clear test components to prevent bleeding"""
    # Clear all test containers
    containers_to_clear = [
        'test_dashboard_container',
        'test_logs_container', 
        'test_settings_container',
        'test_auto_refresh_container'
    ]
    
    for container_key in containers_to_clear:
        if container_key in st.session_state:
            try:
                del st.session_state[container_key]
            except:
                pass
    
    # Clear component tracking
    st.session_state.test_components_rendered = {}

def render_dashboard_with_refresh_test(auto_refresh_enabled):
    """Test dashboard with auto refresh"""
    st.markdown("### 🏠 Dashboard Test (Auto Refresh Enabled)" if auto_refresh_enabled else "### 🏠 Dashboard Test")
    
    # Dashboard-specific components
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Comments", "150", delta="5")
        
    with col2:
        st.metric("Spam Detected", "12", delta="-2")
        
    with col3:
        st.metric("Monitor Status", "Running")
    
    # Dashboard-only components
    st.markdown("#### Recent Posts")
    with st.expander("Post 1", expanded=False):
        st.write("This is a dashboard post")
        st.button("Dashboard Action", key="dashboard_action")
    
    # Track components
    st.session_state.test_components_rendered['dashboard'] = {
        'metrics': 3,
        'posts': 1,
        'buttons': 1,
        'timestamp': datetime.now().strftime('%H:%M:%S')
    }
    
    # Show dashboard-specific status
    st.info("✅ Dashboard components rendered")
    if auto_refresh_enabled:
        st.success("🔄 Auto refresh active for Dashboard")

def render_logs_with_refresh_test(auto_refresh_enabled):
    """Test logs with auto refresh"""
    st.markdown("### 📋 Logs Test (Auto Refresh Enabled)" if auto_refresh_enabled else "### 📋 Logs Test")
    
    # Logs-specific components
    st.markdown("#### Activity Logs")
    
    # Sample logs
    logs = [
        {"time": "10:30:15", "action": "NEW_COMMENT", "status": "✅"},
        {"time": "10:31:22", "action": "SPAM_DETECTED", "status": "❌"},
        {"time": "10:32:45", "action": "COMMENT_DELETED", "status": "🗑️"}
    ]
    
    for i, log in enumerate(logs):
        col1, col2, col3 = st.columns([2, 3, 1])
        with col1:
            st.text(log['time'])
        with col2:
            st.text(log['action'])
        with col3:
            st.text(log['status'])
    
    # Logs-only components
    if st.button("Clear Logs", key="logs_clear"):
        st.success("Logs cleared")
    
    # Track components
    st.session_state.test_components_rendered['logs'] = {
        'log_entries': len(logs),
        'buttons': 1,
        'timestamp': datetime.now().strftime('%H:%M:%S')
    }
    
    # Show logs-specific status
    st.info("✅ Logs components rendered")
    if auto_refresh_enabled:
        st.success("🔄 Auto refresh active for Logs")

def render_settings_with_refresh_test(auto_refresh_enabled):
    """Test settings (should NOT have auto refresh)"""
    st.markdown("### ⚙️ Settings Test")
    
    # Settings-specific components
    st.markdown("#### Configuration")
    
    with st.form("test_settings"):
        api_key = st.text_input("API Key", value="test-key")
        threshold = st.slider("Threshold", 0.0, 1.0, 0.5)
        auto_delete = st.checkbox("Auto Delete")
        
        if st.form_submit_button("Save Settings"):
            st.success("Settings saved!")
    
    # Settings-only components
    st.markdown("#### Advanced Settings")
    if st.button("Reset to Defaults", key="settings_reset"):
        st.info("Settings reset")
    
    # Track components
    st.session_state.test_components_rendered['settings'] = {
        'form_fields': 3,
        'buttons': 2,
        'timestamp': datetime.now().strftime('%H:%M:%S')
    }
    
    # Show settings-specific status
    st.info("✅ Settings components rendered")
    if auto_refresh_enabled:
        st.warning("⚠️ Auto refresh should NOT be active for Settings")

def handle_safe_auto_refresh(page):
    """Handle safe auto refresh without component bleeding"""
    # Create isolated refresh container
    refresh_key = f"auto_refresh_{page}_{int(time.time())}"
    
    if refresh_key not in st.session_state:
        st.session_state[refresh_key] = st.empty()
    
    with st.session_state[refresh_key].container():
        st.markdown("---")
        st.markdown("#### 🔄 Auto Refresh Status")
        
        col1, col2 = st.columns([3, 1])
        with col1:
            st.caption(f"Auto refresh enabled for {page}")
        with col2:
            # Countdown
            countdown_placeholder = st.empty()
            progress_bar = st.progress(0)
            
            for i in range(5, 0, -1):
                countdown_placeholder.text(f"{i}s")
                progress_bar.progress((5-i+1)/5)
                time.sleep(1)
            
            # Clear refresh container before rerun
            try:
                del st.session_state[refresh_key]
            except:
                pass
            
            # Increment refresh count
            st.session_state.test_refresh_count += 1
            
            # Safe rerun
            st.rerun()

def show_component_tracking():
    """Show component tracking information"""
    st.markdown("---")
    st.markdown("### 📊 Component Tracking")
    
    if st.session_state.test_components_rendered:
        for page, components in st.session_state.test_components_rendered.items():
            with st.expander(f"{page.title()} Components", expanded=True):
                for component_type, count in components.items():
                    if component_type != 'timestamp':
                        st.write(f"- {component_type}: {count}")
                st.caption(f"Last rendered: {components.get('timestamp', 'N/A')}")
    else:
        st.info("No components tracked yet")
    
    # Show refresh count
    if st.session_state.test_refresh_count > 0:
        st.metric("Auto Refresh Count", st.session_state.test_refresh_count)
    
    # Test instructions
    st.markdown("#### 🧪 Test Instructions:")
    st.markdown("""
    1. **Enable auto refresh** and select Dashboard or Logs
    2. **Wait for auto refresh** to trigger (5 seconds)
    3. **Check that only current page components** are visible after refresh
    4. **Switch to Settings** and verify auto refresh is disabled
    5. **Switch back to Dashboard/Logs** and verify components are clean
    """)
    
    # Component isolation verification
    current_time = datetime.now().strftime("%H:%M:%S")
    st.caption(f"Test running since: {current_time}")

if __name__ == "__main__":
    # Set page config
    st.set_page_config(
        page_title="Auto Refresh Test",
        page_icon="🔄",
        layout="wide"
    )
    
    # Run the test
    test_auto_refresh_isolation()
