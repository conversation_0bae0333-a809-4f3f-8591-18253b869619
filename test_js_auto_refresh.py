#!/usr/bin/env python3
"""
Test JavaScript Auto Refresh
Simple test to verify JavaScript-based auto refresh works without component bleeding
"""

import streamlit as st
import time
from datetime import datetime

def main():
    st.set_page_config(
        page_title="JS Auto Refresh Test",
        page_icon="🔄",
        layout="wide"
    )
    
    st.title("🔄 JavaScript Auto Refresh Test")
    st.markdown("Testing pure JavaScript auto refresh without st.rerun()")
    
    # Initialize session state
    if 'test_counter' not in st.session_state:
        st.session_state.test_counter = 0
    if 'auto_refresh_enabled' not in st.session_state:
        st.session_state.auto_refresh_enabled = False
    if 'monitor_running' not in st.session_state:
        st.session_state.monitor_running = False
    
    # Controls
    col1, col2 = st.columns(2)
    
    with col1:
        auto_refresh = st.checkbox("🔄 Enable Auto Refresh", value=st.session_state.auto_refresh_enabled)
        st.session_state.auto_refresh_enabled = auto_refresh
    
    with col2:
        monitor_running = st.checkbox("🟢 Monitor Running", value=st.session_state.monitor_running)
        st.session_state.monitor_running = monitor_running
    
    st.markdown("---")
    
    # Page content
    current_time = datetime.now().strftime("%H:%M:%S")
    st.session_state.test_counter += 1
    
    # Metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Page Loads", st.session_state.test_counter)
    
    with col2:
        st.metric("Current Time", current_time)
    
    with col3:
        status = "🟢 Running" if monitor_running else "🔴 Stopped"
        st.metric("Monitor Status", status)
    
    with col4:
        refresh_status = "🔄 Enabled" if auto_refresh else "⏸️ Disabled"
        st.metric("Auto Refresh", refresh_status)
    
    # Sample content
    st.markdown("### 📝 Sample Content")
    
    with st.expander("Sample Post 1", expanded=False):
        st.write(f"This is sample content loaded at {current_time}")
        st.write("This content should remain stable during auto refresh")
        
        if st.button("Test Button 1", key="btn1"):
            st.success("Button 1 clicked!")
    
    with st.expander("Sample Post 2", expanded=False):
        st.write("Another sample post with different content")
        st.write(f"Counter value: {st.session_state.test_counter}")
        
        if st.button("Test Button 2", key="btn2"):
            st.info("Button 2 clicked!")
    
    # JavaScript Auto Refresh Implementation
    if auto_refresh and monitor_running:
        implement_js_auto_refresh()
    
    # Status information
    st.markdown("---")
    st.markdown("### 📊 Test Status")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Settings:**")
        st.write(f"- Auto Refresh: {'✅ Enabled' if auto_refresh else '❌ Disabled'}")
        st.write(f"- Monitor: {'✅ Running' if monitor_running else '❌ Stopped'}")
        st.write(f"- Page Loads: {st.session_state.test_counter}")
    
    with col2:
        st.write("**Expected Behavior:**")
        st.write("- Auto refresh should work when both enabled")
        st.write("- Page should refresh every 5 seconds")
        st.write("- No component duplication should occur")
        st.write("- Content should remain stable")

def implement_js_auto_refresh():
    """Implement JavaScript-only auto refresh"""
    auto_refresh_js = f"""
    <div id="js-auto-refresh-indicator" style="
        position: fixed; 
        top: 70px; 
        right: 20px; 
        background: linear-gradient(135deg, #4CAF50, #45a049); 
        color: white; 
        padding: 8px 15px; 
        border-radius: 20px; 
        font-size: 13px;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        z-index: 9999;
        border: 2px solid rgba(255,255,255,0.2);
    ">
        🔄 Auto refresh: <span id="js-countdown" style="font-weight: bold;">5</span>s
    </div>
    
    <script>
    (function() {{
        // Prevent multiple timers
        if (window.jsAutoRefreshTimer) {{
            clearInterval(window.jsAutoRefreshTimer);
        }}
        
        let countdown = 5;
        const countdownElement = document.getElementById('js-countdown');
        const indicatorElement = document.getElementById('js-auto-refresh-indicator');
        
        window.jsAutoRefreshTimer = setInterval(() => {{
            countdown--;
            if (countdownElement) {{
                countdownElement.textContent = countdown;
            }}
            
            // Change color as countdown approaches zero
            if (indicatorElement) {{
                if (countdown <= 2) {{
                    indicatorElement.style.background = 'linear-gradient(135deg, #FF9800, #F57C00)';
                }} else if (countdown <= 1) {{
                    indicatorElement.style.background = 'linear-gradient(135deg, #F44336, #D32F2F)';
                }}
            }}
            
            if (countdown <= 0) {{
                clearInterval(window.jsAutoRefreshTimer);
                
                // Show refreshing status
                if (indicatorElement) {{
                    indicatorElement.innerHTML = '🔄 Refreshing...';
                    indicatorElement.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
                }}
                
                // Refresh the page
                setTimeout(() => {{
                    const currentUrl = new URL(window.location);
                    currentUrl.searchParams.set('_refresh', Date.now());
                    window.location.replace(currentUrl.toString());
                }}, 500);
            }}
        }}, 1000);
        
        // Clean up on page unload
        window.addEventListener('beforeunload', () => {{
            if (window.jsAutoRefreshTimer) {{
                clearInterval(window.jsAutoRefreshTimer);
            }}
        }});
    }})();
    </script>
    """
    
    st.markdown(auto_refresh_js, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
